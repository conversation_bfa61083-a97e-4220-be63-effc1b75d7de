/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.app.cargill.document.UserJourneyDocument;
import com.app.cargill.document.UserJourneyPath;
import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.dto.UserJourneyRequestDto;
import com.app.cargill.model.UserJourney;
import com.app.cargill.repository.UserJourneyRepository;
import com.app.cargill.service.IUserJourneyService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserJourneyServiceImpl implements IUserJourneyService {

  private final UserJourneyRepository userJourneyRepository;
  @Override
  public UserJourneyDto save(UserJourneyRequestDto userJourneyRequestDto) {
    log.debug("Saving user journey data for user: {}", userJourneyRequestDto.getUserEmail());

    UserJourney userJourney = dtoToModel(userJourneyRequestDto);

    return modelToDto(userJourneyRepository.save(userJourney));
  }

  @Override
  public List<UserJourneyDto> getByUpdatedDateAfter(Instant lastUpdatedDate) {
    log.debug("Fetching user journey data updated after: {}", lastUpdatedDate);

    List<UserJourney> userJourneys = userJourneyRepository.findByUpdatedDateAfter(lastUpdatedDate);

    return userJourneys.stream()
        .map(this::modelToDto)
        .collect(Collectors.toList());
  }

  private UserJourney dtoToModel(UserJourneyRequestDto dto) {
    List<UserJourneyPath> paths = dto.getPaths().stream()
        .map(pathDto -> UserJourneyPath.builder()
            .path(pathDto.getPath())
            .eventName(pathDto.getEventName())
            .eventTriggerTime(pathDto.getEventTriggerTime())
            .build())
        .collect(Collectors.toList());

    UserJourneyDocument document = UserJourneyDocument.builder()
        .id(UUID.randomUUID())
        .userEmail(dto.getUserEmail())
        .paths(paths)
        .createTimeUtc(Instant.now())
        .lastModifiedTimeUtc(Instant.now())
        .isNew(true)
        .isDeleted(false)
        .build();

    return UserJourney.builder()
        .userJourneyDocument(document)
        .build();
  }

  private UserJourneyDto modelToDto(UserJourney userJourney) {
    List<UserJourneyPathDto> pathDtos = userJourney.getUserJourneyDocument().getPaths().stream()
        .map(path -> UserJourneyPathDto.builder()
            .path(path.getPath())
            .eventName(path.getEventName())
            .eventTriggerTime(path.getEventTriggerTime())
            .build())
        .collect(Collectors.toList());

    return UserJourneyDto.builder()
        .id(userJourney.getUserJourneyDocument().getId())
        .userEmail(userJourney.getUserJourneyDocument().getUserEmail())
        .paths(pathDtos)
        .createdDate(userJourney.getCreatedDate() != null ? userJourney.getCreatedDate().toInstant() : null)
        .updatedDate(userJourney.getUpdatedDate() != null ? userJourney.getUpdatedDate().toInstant() : null)
        .deleted(userJourney.getUserJourneyDocument().isDeleted())
        .build();
  }
}
