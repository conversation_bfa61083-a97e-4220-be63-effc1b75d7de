/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.app.cargill.document.UserAnalyticDocument;
import com.app.cargill.document.UserJourneyDocument;
import com.app.cargill.document.UserJourneyPath;
import com.app.cargill.dto.UserAnalyticsDto;
import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.dto.UserJourneyRequestDto;
import com.app.cargill.model.UserAnalytics;
import com.app.cargill.model.UserJourney;
import com.app.cargill.repository.UserAnalyticRepository;
import com.app.cargill.service.IUserJourneyService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserJourneyServiceImpl implements IUserJourneyService {

  private final UserAnalyticRepository userAnalyticRepository;
  @Override
  public UserJourneyDto save(UserJourneyRequestDto userJourneyRequestDto) {
    log.debug("Saving user journey data for user: {}", userJourneyRequestDto.getUserEmail());
    
    UserAnalytics userAnalytics = dtoToModel(userJourneyRequestDto);
    
    return modelToDto(userAnalyticRepository.save(userAnalytics));
  }

  @Override
  public List<UserJourneyDto> getByUpdatedDateAfter(Instant lastUpdatedDate) {
    log.debug("Fetching user journey data updated after: {}", lastUpdatedDate);
    
    List<UserAnalytics> userAnalytics = userAnalyticRepository.findByUpdatedDateAfter(lastUpdatedDate);
    
    return userAnalytics.stream()
        .map(this::modelToDto)
        .toList();
  }

  private UserAnalytics dtoToModel(UserJourneyRequestDto dto) {
    List<UserJourneyPath> paths = dto.getPaths().stream()
        .map(pathDto -> UserJourneyPath.builder()
            .path(pathDto.getPath())
            .eventName(pathDto.getEventName())
            .eventTriggerTime(pathDto.getEventTriggerTime())
            .build())
        .toList();

    UserAnalyticDocument document = UserAnalyticDocument.builder()
        .id(UUID.randomUUID())
        .userEmail(dto.getUserEmail())
        .paths(paths)
        .createTimeUtc(Instant.now())
        .lastModifiedTimeUtc(Instant.now())
        .isNew(true)
        .isDeleted(false)
        .build();

    return UserJourney.builder()
        .userJourneyDocument(document)
        .build();
  }

  private UserJourneyDto modelToDto(UserAnalytics userAnalytics) {
    List<UserAnalyticsDto> pathDtos = userAnalytics.getUserAnalyticDocument().getPaths().stream()
        .map(path -> UserJourneyPathDto.builder()
            .path(path.getPath())
            .eventName(path.getEventName())
            .eventTriggerTime(path.getEventTriggerTime())
            .build())
        .toList();

    return UserAnalyticsDto.builder()
        .id(userAnalytics.getUserAnalyticDocument().getId())
        .userEmail(userAnalytics.getUserAnalyticDocument().getUserEmail())
        .paths(pathDtos)
        .createdDate(userAnalytics.getCreatedDate() != null ? userAnalytics.getCreatedDate().toInstant() : null)
        .updatedDate(userAnalytics.getUpdatedDate() != null ? userAnalytics.getUpdatedDate().toInstant() : null)
        .deleted(userAnalytics.getUserAnalyticDocument().isDeleted())
        .build();
  }
}
