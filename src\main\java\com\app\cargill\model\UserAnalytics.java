/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.app.cargill.document.UserAnalyticDocument;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Table(name = "user_analytics")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
public class UserAnalytics extends BaseEntity {

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb")
  private UserAnalyticDocument userAnalyticDocument;
}
