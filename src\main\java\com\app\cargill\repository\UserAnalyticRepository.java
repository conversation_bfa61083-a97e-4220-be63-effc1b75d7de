/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import java.time.Instant;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.app.cargill.model.UserAnalytics;

@Repository
public interface UserAnalyticRepository
    extends JpaRepository<UserAnalytics, Long>, JpaSpecificationExecutor<UserAnalytics> {

  @Query(
      value =
          "SELECT uj.* FROM user_journey uj WHERE timezone('UTC', uj.updated_date) > :lastUpdatedDate AND uj.deleted = false",
      nativeQuery = true)
  List<UserAnalytics> findByUpdatedDateAfter(@Param("lastUpdatedDate") Instant lastUpdatedDate);

  @Query(
      value = "SELECT uj.* FROM user_journey uj WHERE uj.user_journey_document ->> 'UserEmail' = :userEmail AND uj.deleted = false",
      nativeQuery = true)
  List<UserAnalytics> findByUserEmail(@Param("userEmail") String userEmail);
}
