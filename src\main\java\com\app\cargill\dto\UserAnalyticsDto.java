/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.io.Serializable;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserAnalyticsDto implements Serializable {

  private static final long serialVersionUID = 1L;

  private UUID id;
  private String path;
  private String eventName;
  private String eventTriggerTime;
}
